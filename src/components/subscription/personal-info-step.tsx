import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSignaturePad } from "@/hooks/use-signature-pad";
import { Trash2 } from "lucide-react";
import { useState, useCallback } from "react";
import TermAndConditionDialog from "./term-and-conditions-dialog";
import { PersonalInfo } from "./types";
import { Plan } from "@/types/membership";
import { z } from "zod";
import ValidationError from "../forms/validation-error";
import { useAuthStore } from "@/store/auth-store";

interface PersonalInfoStepProps {
  personalInfo: PersonalInfo;
  onUpdateInfo: (info: PersonalInfo) => void;
  selectedPlan: Plan | null;
}

// Zod validation schema
const personalInfoSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters"),

  mobile: z
    .string()
    .min(1, "Mobile number is required")
    .regex(/^\+?[\d\s\-\(\)]{10,}$/, "Please enter a valid mobile number"),

  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email must be less than 254 characters"),

  hasAgreedToWaiver: z
    .boolean()
    .refine((val) => val === true, "You must agree to the terms and conditions"),

  isAbove18: z.boolean().refine((val) => val === true, "You must be 18 years or older"),
});

type FormErrors = Partial<Record<keyof PersonalInfo, string>>;

interface SignatureError {
  signature?: string;
}

export default function PersonalInfoStep({
  personalInfo,
  onUpdateInfo,
  selectedPlan,
}: PersonalInfoStepProps) {
  const [errors, setErrors] = useState<FormErrors>({});
  const [signatureError, setSignatureError] = useState<SignatureError>({});
  const { user, isAuthenticated } = useAuthStore();

  // Validate individual field
  const validateField = useCallback((field: keyof PersonalInfo, value: any) => {
    try {
      const fieldSchema = personalInfoSchema.shape[field];
      fieldSchema.parse(value);
      setErrors((prev) => ({ ...prev, [field]: undefined }));
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        setErrors((prev) => ({ ...prev, [field]: error.errors[0]?.message }));
      }
      return false;
    }
  }, []);

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    try {
      personalInfoSchema.parse(personalInfo);
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: FormErrors = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as keyof PersonalInfo] = err.message;
          }
        });
        setErrors(newErrors);
      }
      return false;
    }
  }, [personalInfo]);

  const handleChange = (field: keyof PersonalInfo, value: string | boolean) => {
    const updatedInfo = {
      ...personalInfo,
      [field]: value,
    };
    onUpdateInfo(updatedInfo);

    // Validate field on change
    validateField(field, value);
  };

  const {
    canvasRef,
    isEmpty: isSignatureEmpty,
    clearSignature,
    getSignatureData,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
  } = useSignaturePad();

  return (
    <div className="w-full rounded-lg bg-gradient-to-br from-white to-[#FFFAED] p-6 lg:max-w-5xl">
      <div className="mb-8 text-center">
        <h2 className="mb-2 text-gray-900">Personal Information</h2>
        <p className="text-gray-600">Please provide your details to continue</p>
      </div>

      {/* Selected Membership Display */}
      {selectedPlan && (
        <div className="mx-auto mb-8 max-w-md">
          <div className="rounded-lg bg-gradient-to-r from-[#1C5534] to-[#0f3a1f] p-4 text-white">
            <div className="text-center">
              <h3 className="mb-1 text-white">Selected Membership</h3>
              <p className="mb-2 text-lg text-white">{selectedPlan.name}</p>
              {selectedPlan.name !== "Pay-to-Play" && (
                <div className="flex items-baseline justify-center space-x-1">
                  <span className="text-2xl text-white">
                    ${selectedPlan.package_prices.find((p) => p.frequency === "M")?.amount || 0}
                  </span>
                  <span className="text-sm text-green-100">/month</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="mx-auto max-w-md space-y-6">
        <div>
          <Label htmlFor="name">Full Name</Label>
          <Input
            id="name"
            type="text"
            placeholder="Enter your full name"
            value={personalInfo.name}
            onChange={(e) => handleChange("name", e.target.value)}
            className={`mt-2 ${errors.name ? "border-red-500" : ""}`}
            aria-invalid={!!errors.name}
            aria-describedby={errors.name ? "name-error" : undefined}
          />
          {errors.name && <ValidationError id="name-error" className="pt-1" error={errors.name} />}
        </div>

        <div>
          <Label htmlFor="mobile">Mobile Number</Label>
          <Input
            id="mobile"
            type="tel"
            placeholder="Enter your mobile number"
            maxLength={10}
            minLength={10}
            value={personalInfo.mobile}
            onChange={(e) => handleChange("mobile", e.target.value)}
            className={`mt-2 ${errors.mobile ? "border-red-500" : ""}`}
            aria-invalid={!!errors.mobile}
            aria-describedby={errors.mobile ? "mobile-error" : undefined}
          />
          {errors.mobile && (
            <ValidationError id="mobile-error" className="pt-1" error={errors.mobile} />
          )}
        </div>

        <div>
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            value={personalInfo.email}
            onChange={(e) => handleChange("email", e.target.value)}
            className={`mt-2 ${errors.email ? "border-red-500" : ""}`}
            aria-invalid={!!errors.email}
            aria-describedby={errors.email ? "email-error" : undefined}
          />
          {errors.email && (
            <ValidationError id="email-error" className="pt-1" error={errors.email} />
          )}
        </div>

        {/* Signature */}
        <div className="flex flex-col space-y-2">
          <label className="text-sm font-medium text-gray-700">Signature</label>
          <div className="relative">
            <canvas
              ref={canvasRef}
              width={448}
              height={100}
              className="h-[100px] w-full cursor-crosshair touch-none rounded-lg border border-black bg-white"
              style={{ height: "100px", maxWidth: "448px" }}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            />
            <button
              type="button"
              onClick={clearSignature}
              className="absolute top-2 right-2 p-1 text-gray-500 transition-colors hover:text-red-500"
              title="Clear signature"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
          {signatureError.signature && (
            <span className="text-sm text-red-500">{signatureError.signature}</span>
          )}
        </div>

        {/* Age Verification */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="age-verification"
              checked={personalInfo.isAbove18}
              onCheckedChange={(checked) => handleChange("isAbove18", checked as boolean)}
              className="mt-1"
              aria-invalid={!!errors.isAbove18}
              aria-describedby={errors.isAbove18 ? "age-error" : undefined}
            />
            <div className="flex-1">
              <Label htmlFor="age-verification" className="cursor-pointer text-sm">
                I confirm that I am 18 years of age or older
              </Label>
            </div>
          </div>
          {errors.isAbove18 && <ValidationError id="age-error" error={errors.isAbove18} />}
        </div>

        {/* Liability Waiver */}
        <div className="border-t border-gray-200 pt-6">
          <div className="flex items-start space-x-3">
            <Checkbox
              id="liability-waiver"
              checked={personalInfo.hasAgreedToWaiver}
              onCheckedChange={(checked) => handleChange("hasAgreedToWaiver", checked as boolean)}
              className="mt-1"
              aria-invalid={!!errors.hasAgreedToWaiver}
              aria-describedby={errors.hasAgreedToWaiver ? "waiver-error" : undefined}
            />
            <div className="flex-1">
              <Label htmlFor="liability-waiver" className="cursor-pointer text-sm">
                I have read and agree to the <TermAndConditionDialog />
              </Label>
            </div>
          </div>
          {errors.hasAgreedToWaiver && (
            <ValidationError id="waiver-error" error={errors.hasAgreedToWaiver} />
          )}
        </div>
      </div>
    </div>
  );
}
