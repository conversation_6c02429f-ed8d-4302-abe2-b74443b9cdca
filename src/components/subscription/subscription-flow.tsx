"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

import { steps, existingPaymentMethods } from "./constants";
import Stepper from "./stepper";
import MembershipStep from "./membership-step";
import PersonalInfoStep from "./personal-info-step";
import CheckoutStep from "./checkout-step";
import SuccessStep from "./success-step";
import { useSubscriptionStore } from "@/store/subscription-store";
import { useAuthStore } from "@/store/auth-store";

export default function SubscriptionFlow() {
  const subscriptionFlowRef = React.useRef<HTMLDivElement>(null);
  const {
    currentStep,
    selectedPlan,
    personalInfo,
    selectedPaymentMethod,
    setCurrentStep,
    setSelectedPlan,
    setPersonalInfo,
    setSelectedPaymentMethod,
    canProceedToNextStep,
    populateUserData,
  } = useSubscriptionStore();

  const { user, isAuthenticated } = useAuthStore();

  // Populate user data when component mounts if user is logged in
  useEffect(() => {
    if (isAuthenticated && user) {
      populateUserData({
        name: user.name,
        email: user.user_email?.email,
        phone: user.user_phone?.phone,
      });
    }
  }, [isAuthenticated, user, populateUserData]);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
      subscriptionFlowRef.current?.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleComplete = () => {
    setCurrentStep(4);
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      subscriptionFlowRef.current?.scrollTo({ top: 0, behavior: "smooth" });
    }
  };

  const handleStepClick = (stepId: number) => {
    // Only allow navigation to completed steps
    if (stepId < currentStep && currentStep !== 4) {
      setCurrentStep(stepId);
    }
  };

  return (
    <div
      className="m-0 min-h-screen w-full lg:m-6"
      id="subscription-flow"
      ref={subscriptionFlowRef}
    >
      <div className="w-full">
        {/* Header */}
        <div className="mb-8 px-4 text-center">
          <h1 className="mb-2 text-4xl font-bold text-gray-900">Join Epic Charlotte</h1>
          <p className="text-gray-600">Choose your membership and get started in minutes</p>
        </div>

        {/* Stepper */}
        <div className="mx-auto mb-8 max-w-4xl px-4">
          <Stepper steps={steps} currentStep={currentStep} onStepClick={handleStepClick} />
        </div>

        {/* Step Content */}
        <Card className="flex w-full justify-center rounded-none border-0">
          <CardContent className="flex w-full justify-center py-8 shadow-none hover:shadow-none">
            {currentStep === 1 && (
              <MembershipStep
                selectedPlan={selectedPlan}
                onSelectPlan={setSelectedPlan}
                onNext={handleNext}
              />
            )}
            {currentStep === 2 && (
              <PersonalInfoStep
                personalInfo={personalInfo}
                onUpdateInfo={setPersonalInfo}
                selectedPlan={selectedPlan}
              />
            )}
            {currentStep === 3 && (
              <CheckoutStep
                selectedPlan={selectedPlan}
                personalInfo={personalInfo}
                existingPaymentMethods={existingPaymentMethods}
                selectedPaymentMethod={selectedPaymentMethod}
                onSelectPaymentMethod={setSelectedPaymentMethod}
                onComplete={handleComplete}
              />
            )}
            {currentStep === 4 && (
              <SuccessStep
                selectedPlan={selectedPlan}
                personalInfo={personalInfo}
                selectedPaymentMethod={selectedPaymentMethod}
              />
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        {currentStep > 1 && currentStep < 4 && (
          <div className="mx-auto max-w-4xl px-4">
            <div className="mt-8 flex justify-between">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1}
                className="bg-white px-6 text-black"
              >
                Back
              </Button>
              <Button
                onClick={handleNext}
                disabled={!canProceedToNextStep()}
                className={`bg-[#1C5534] px-6 text-white hover:bg-[#0f3a1f] ${currentStep === 3 ? "hidden" : "block"}`}
              >
                Continue
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
