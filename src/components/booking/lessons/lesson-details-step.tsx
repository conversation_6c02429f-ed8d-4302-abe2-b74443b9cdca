import { useGetPublicInstructorList } from "@/api/booking-instructor-service";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronDown, ChevronDownIcon, ChevronLeft, Minus, Plus } from "lucide-react";
import React, { useCallback, useMemo, useState, useEffect } from "react";
import { Button } from "../../ui/button";
import { Calendar } from "../../ui/calendar";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { extractErrorMessage } from "@/libs/utils";
import Loader from "../../loader";
import { Lesson } from "./booking-lesson-section";
import { useBookingStore } from "@/store/booking-store";
import {
  useGetNextInstructorOpening,
  useGetPublicCourtById,
  useGetPublicCourtList,
} from "@/api/booking-service";
import { Court } from "@/types/booking";
import { useGetPlayerList } from "@/api/booking-checkout-service";
import LessonPlayerManagementDialog from "./lesson-player-management-dialog";
import { useLessonBookingEstimate } from "@/hooks/use-booking-estimate";
import { Skeleton } from "@/components/ui/skeleton";

// Type for instructor object from API
interface Instructor {
  id: number;
  name?: string;
  [key: string]: any; // Allow for additional properties
}

interface LessonDetailsStepProps {
  lesson: Lesson;
  onBack: () => void;
  locationId: number;
}

// Player interface for the dialog
interface Player {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  country_code?: string;
  isSelected: boolean;
  isCurrentUser?: boolean;
}

const LessonDetailsStep = ({ lesson, onBack, locationId }: LessonDetailsStepProps) => {
  const router = useRouter();
  const [isBooking, setIsBooking] = useState(false);
  const { setLessonBookingDetails, addLessonParticipants, lessonBookingDetails } =
    useBookingStore();

  // Player management state
  const [isPlayerDialogOpen, setIsPlayerDialogOpen] = useState(false);
  const [players, setPlayers] = useState<Player[]>([]);
  const [selectedParticipants, setSelectedParticipants] = useState<number[]>([]);

  const [open, setOpen] = React.useState(false);
  const [date, setDate] = React.useState<Date | undefined>(undefined);

  // Initialize selectedParticipants from lessonBookingDetails
  useEffect(() => {
    if (lessonBookingDetails?.participants) {
      setSelectedParticipants(lessonBookingDetails.participants);
    }
  }, [lessonBookingDetails?.participants]);

  // Parse date from lessonBookingDetails for calendar component
  useEffect(() => {
    if (lessonBookingDetails?.date) {
      const parsedDate = new Date(lessonBookingDetails.date);
      if (!isNaN(parsedDate.getTime())) {
        setDate(parsedDate);
      }
    }
  }, [lessonBookingDetails?.date]);

  // Helper function to get initials for avatar fallback
  const getInitials = useCallback((name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }, []);

  const { publicCourtList, publicCourtListLoading } = useGetPublicCourtList({
    page: 1,
    limit: 10,
    courtLocationId: locationId,
  });

  const firstCourtId = useMemo(() => {
    return publicCourtList && publicCourtList.length > 0 ? publicCourtList[0].id : null;
  }, [publicCourtList]);

  const { publicCourtDetails } = useGetPublicCourtById({
    courtId: firstCourtId,
  });

  // Get instructor list
  const { publicInstructorList, publicInstructorListLoading, publicInstructorListError } =
    useGetPublicInstructorList({ page: 1, limit: 100 });

  // Get player list for dialog
  const { playerList, playerListLoading, revalidateGetPlayerList } = useGetPlayerList();

  const { nextInstructorOpening, nextInstructorOpeningLoading } = useGetNextInstructorOpening({
    lessonTypeId: lesson.id,
    date: lessonBookingDetails?.date || "",
    instructorId: lessonBookingDetails?.instructor_id || 0,
    subCourtId: lessonBookingDetails?.sub_court_id || 0,
    duration: lessonBookingDetails?.duration || lesson.default_duration || 60,
  });

  // Helper function to update lesson booking details
  const updateLessonBookingDetails = useCallback(
    (updates: Partial<typeof lessonBookingDetails>) => {
      const currentDetails = lessonBookingDetails || {
        lesson_id: lesson.id,
        instructor_id: 0,
        lesson_type_id: lesson.id,
        type: "Beginner",
        start_time: "",
        duration: lesson.min_duration || 60,
        sub_court_id: 0,
        date: "",
        time: "",
        players: lesson.min_player || 0,
        lesson_name: lesson.name,
        instructor_name: "",
        sub_court_name: "",
        split: false,
      };

      setLessonBookingDetails({
        ...currentDetails,
        ...updates,
      });
    },
    [lessonBookingDetails, lesson, setLessonBookingDetails]
  );

  // Generate duration options based on lesson constraints
  const durationOptions = useMemo(() => {
    const options = [];
    const minDuration = lesson.min_duration || 30;
    const maxDuration = lesson.max_duration || 120;
    const defaultDuration = lesson.default_duration || 60;

    for (let duration = minDuration; duration <= maxDuration; duration += 30) {
      options.push({
        value: duration,
        label: `${duration} minutes`,
        isDefault: duration === defaultDuration,
      });
    }
    return options;
  }, [lesson]);

  // Handler for instructor selection
  const handleInstructorSelect = useCallback(
    (instructor: Instructor) => {
      updateLessonBookingDetails({
        instructor_id: instructor.id,
        instructor_name: instructor.name || "",
      });
    },
    [updateLessonBookingDetails]
  );

  // Handler for duration selection
  const handleDurationSelect = useCallback(
    (duration: number) => {
      updateLessonBookingDetails({
        duration: duration,
      });
    },
    [updateLessonBookingDetails]
  );

  // Prepare estimate request data
  const estimateRequestData = useMemo(() => {
    if (
      !lessonBookingDetails ||
      !lessonBookingDetails.instructor_id ||
      !lessonBookingDetails.date ||
      !lessonBookingDetails.time
    ) {
      return null;
    }

    return {
      lesson_id: lessonBookingDetails.lesson_id,
      instructor_id: lessonBookingDetails.instructor_id,
      lesson_type_id: lessonBookingDetails.lesson_type_id,
      type: lessonBookingDetails.type || "Beginner",
      start_time: `${lessonBookingDetails.date} ${lessonBookingDetails.time}:00`,
      duration: lessonBookingDetails.duration,
      sub_court_id: lessonBookingDetails.sub_court_id,
      split: lessonBookingDetails.split || false,
      participants: lessonBookingDetails.participants || [],
    };
  }, [lessonBookingDetails]);

  const {
    estimateData,
    isLoading: estimateLoading,
    error: estimateError,
  } = useLessonBookingEstimate(estimateRequestData);

  // Player management functions
  const loadPlayers = useCallback(() => {
    if (playerListLoading || !playerList) return;

    console.log("start loading users");
    const formattedPlayers: Player[] = playerList.map((player: any) => ({
      id: player.id.toString(),
      name: player.name || `Player ${player.id}`,
      email: player.email,
      phone: player.phone,
      country_code: player.country_code,
      isSelected: selectedParticipants.includes(player.id),
      isCurrentUser: false, // For lessons, we don't need to mark current user
    }));

    setPlayers(formattedPlayers);
  }, [playerList, playerListLoading, selectedParticipants]);

  // Auto-reload players when playerList data changes (e.g., after revalidation)
  useEffect(() => {
    if (playerList && !playerListLoading) {
      loadPlayers();
    }
  }, [playerList, playerListLoading, loadPlayers]);

  const handlePlayerToggle = useCallback((playerId: string) => {
    setPlayers((prevPlayers) =>
      prevPlayers.map((player) =>
        player.id === playerId ? { ...player, isSelected: !player.isSelected } : player
      )
    );
    setSelectedParticipants((prevParticipants) =>
      prevParticipants.includes(parseInt(playerId))
        ? prevParticipants.filter((id) => id !== parseInt(playerId))
        : [...prevParticipants, parseInt(playerId)]
    );
  }, []);

  const handleOpenPlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(true);
    loadPlayers();
  }, [loadPlayers]);

  const handleClosePlayerDialog = useCallback(() => {
    setIsPlayerDialogOpen(false);

    // Get selected player IDs
    const selectedPlayerIds = players
      .filter((player) => player.isSelected)
      .map((player) => parseInt(player.id))
      .filter((id) => !isNaN(id));

    setSelectedParticipants(selectedPlayerIds);
    addLessonParticipants(selectedPlayerIds);

    console.log("Selected participants saved to booking store:", selectedPlayerIds);
  }, [players, addLessonParticipants]);

  const handlePlayerAdded = useCallback(() => {
    // Reload players when a new player is added
    loadPlayers();
  }, [loadPlayers]);

  // Handler for booking now
  const handleBookNow = useCallback(async () => {
    try {
      setIsBooking(true);

      if (!lessonBookingDetails) {
        toast.error("Please complete all booking details");
        return;
      }

      // Validate required fields
      if (
        !lessonBookingDetails.instructor_name ||
        !lessonBookingDetails.date ||
        !lessonBookingDetails.time ||
        !lessonBookingDetails.duration
      ) {
        toast.error("Please fill in all required fields");
        return;
      }

      // Update start_time in the correct format
      const updatedDetails = {
        ...lessonBookingDetails,
        start_time: `${lessonBookingDetails.date} ${lessonBookingDetails.time}:00`,
      };

      console.log("Saving lesson booking details to store:", updatedDetails);
      setLessonBookingDetails(updatedDetails);

      toast.success("Lesson booking details saved! Redirecting to checkout...");
      router.push("/checkout?booking_type=lesson");
    } catch (error: any) {
      console.error("Error saving lesson booking details:", error);
      toast.error(extractErrorMessage(error) || "Failed to save lesson booking details");
    } finally {
      setIsBooking(false);
    }
  }, [lessonBookingDetails, setLessonBookingDetails, router]);

  return (
    <div className="container mx-auto py-1 lg:px-4">
      <div className="flex w-full flex-col items-start justify-start gap-4 lg:gap-10">
        <div className="flex w-full flex-col items-start justify-start gap-4 rounded-[30px] bg-white p-4 lg:flex-row lg:items-start lg:justify-between lg:gap-10 lg:p-10">
          {/* Left Section - Form Controls */}
          <div className="flex w-full flex-col items-start justify-start gap-3 lg:w-1/2">
            {/* Back Button */}
            <button
              onClick={onBack}
              className="flex items-center gap-2 text-[#1c5534] transition-colors hover:text-[#0f3a1f]"
            >
              <ChevronLeft className="h-8 w-8" />
            </button>

            {/* Lesson Display */}
            <div className="mb-1 flex flex-col items-start justify-start self-stretch overflow-hidden rounded-xl bg-white pb-2.5">
              <div className="flex flex-col items-start justify-start gap-2.5 self-stretch px-5 py-2.5">
                <div className="font-helvetica text-xl font-bold text-black">{lesson.name}</div>
                <div className="flex flex-col items-start justify-start self-stretch">
                  <div className="inline-flex items-center justify-start self-stretch">
                    <div className="relative size-6 overflow-hidden">
                      <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                        <svg
                          width="18"
                          height="18"
                          viewBox="0 0 18 18"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M8.69922 4.3502V8.7002L11.5992 10.1502M15.9492 8.7002C15.9492 12.7043 12.7033 15.9502 8.69922 15.9502C4.69516 15.9502 1.44922 12.7043 1.44922 8.7002C1.44922 4.69613 4.69516 1.4502 8.69922 1.4502C12.7033 1.4502 15.9492 4.69613 15.9492 8.7002Z"
                            stroke="#1C5534"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="font-helvetica justify-start text-sm font-normal text-black">
                      Minimum {lesson.min_duration} mins
                    </div>
                  </div>
                  <div className="inline-flex items-center justify-start self-stretch">
                    <div className="relative size-6 overflow-hidden">
                      <div data-svg-wrapper className="absolute top-[3px] left-[3px]">
                        <svg
                          width="18"
                          height="18"
                          viewBox="0 0 18 18"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M15.9492 15.2248V13.7748C15.9492 12.4235 15.025 11.2881 13.7742 10.9662M11.2367 2.3856C12.2995 2.81581 13.0492 3.85776 13.0492 5.07481C13.0492 6.29186 12.2995 7.3338 11.2367 7.76401M12.3242 15.2248C12.3242 13.8736 12.3242 13.198 12.1035 12.665C11.8091 11.9544 11.2446 11.3899 10.534 11.0956C10.0011 10.8748 9.32545 10.8748 7.97422 10.8748H5.79922C4.44799 10.8748 3.77237 10.8748 3.23944 11.0956C2.52886 11.3899 1.9643 11.9544 1.66997 12.665C1.44922 13.198 1.44922 13.8736 1.44922 15.2248M9.78672 5.07481C9.78672 6.67643 8.48835 7.97481 6.88672 7.97481C5.28509 7.97481 3.98672 6.67643 3.98672 5.07481C3.98672 3.47318 5.28509 2.1748 6.88672 2.1748C8.48835 2.1748 9.78672 3.47318 9.78672 5.07481Z"
                            stroke="#1C5534"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                    </div>
                    <div className="font-helvetica justify-center text-sm font-normal text-black">
                      Min Players: {lesson.min_player} - Max Players : {lesson.max_player}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Instructor Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Instructor
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="font-helvetica flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-6 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50"
                >
                  <div className="flex items-center gap-3">
                    {lessonBookingDetails?.instructor_id &&
                      lessonBookingDetails.instructor_id > 0 && (
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={
                              publicInstructorList.find(
                                (i: Instructor) => i.id === lessonBookingDetails.instructor_id
                              )?.profile_image?.path || "/imgs/profile.png"
                            }
                            alt="Selected instructor"
                            className="object-cover"
                          />
                          <AvatarFallback className="bg-[#ddba0a] text-xs font-semibold text-[#1c5534]">
                            {getInitials(
                              publicInstructorList.find(
                                (i: Instructor) => i.id === lessonBookingDetails.instructor_id
                              )?.name || "Instructor"
                            )}
                          </AvatarFallback>
                        </Avatar>
                      )}
                    <span className="font-helvetica">
                      {lessonBookingDetails?.instructor_name || "Choose instructor"}
                    </span>
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white p-4 shadow-lg">
                {publicInstructorListLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader />
                  </div>
                ) : publicInstructorListError ? (
                  <div className="rounded-[15px] border border-red-200 bg-red-50 p-4 text-center text-red-600">
                    Error loading instructors
                  </div>
                ) : (
                  <div className="grid grid-cols-6 gap-1">
                    {publicInstructorList.map((instructor: Instructor) => (
                      <div
                        key={instructor.id}
                        onClick={() => handleInstructorSelect(instructor)}
                        className="flex cursor-pointer flex-col items-center gap-2 rounded-[15px] p-3 transition-all hover:bg-gray-50"
                      >
                        <Avatar className="h-12 w-12 shadow-sm">
                          <AvatarImage
                            src={instructor.profile_image?.path || "/imgs/profile.png"}
                            alt={instructor.name || `Instructor ${instructor.id}`}
                            className="object-cover"
                          />
                          <AvatarFallback className="bg-[#ddba0a] text-sm font-semibold text-[#1c5534]">
                            {getInitials(instructor.name || `Instructor ${instructor.id}`)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="text-center">
                          <div className="font-helvetica line-clamp-2 text-xs font-medium text-black">
                            {instructor.name || `Instructor ${instructor.id}`}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </PopoverContent>
            </Popover>

            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Court
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {lessonBookingDetails?.sub_court_name || "Select court"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] min-w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {publicCourtListLoading ? (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading courts...
                    </span>
                  </DropdownMenuItem>
                ) : publicCourtDetails && publicCourtDetails?.sub_courts?.length > 0 ? (
                  publicCourtDetails?.sub_courts.map((court: Court) => (
                    <DropdownMenuItem
                      key={court.id}
                      onClick={() => {
                        updateLessonBookingDetails({
                          sub_court_id: court.id,
                          sub_court_name: court.name || `Court ${court.id}`,
                        });
                      }}
                      className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                    >
                      <span className="font-helvetica text-[15px] font-normal text-black">
                        {court.name || `Court ${court.id}`}
                      </span>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem className="cursor-default px-[25px] py-3">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No courts available
                    </span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Date Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select date
            </div>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  id="date"
                  className="font-helvetica flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-6 text-[15px] leading-[35px] font-normal text-black transition-colors hover:bg-gray-50"
                >
                  {date
                    ? `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`
                    : "Select date"}
                  <ChevronDownIcon />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-auto overflow-hidden border-gray-200 p-0 shadow-lg"
                align="start"
              >
                <Calendar
                  mode="single"
                  selected={date}
                  captionLayout="dropdown"
                  onSelect={(date) => {
                    const year = date!.getFullYear();
                    const month = String(date!.getMonth() + 1).padStart(2, "0");
                    const day = String(date!.getDate()).padStart(2, "0");
                    const formattedDate = `${year}-${month}-${day}`;

                    setDate(date);
                    updateLessonBookingDetails({
                      date: formattedDate,
                    });
                    setOpen(false);
                  }}
                  disabled={(date) => date < new Date()}
                  className="rounded-md border"
                />
              </PopoverContent>
            </Popover>

            {/* Time Selection */}
            <div className="font-helvetica w-full text-base leading-[35px] font-normal text-black">
              Select Time
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-[25px] py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {lessonBookingDetails?.time || "Select time"}
                </span>
                <ChevronDown className="h-4 w-4" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] max-w-[90vw] rounded-[20px] border border-[#c3c3c3] bg-white p-4 shadow-lg">
                {nextInstructorOpeningLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      Loading times...
                    </span>
                  </div>
                ) : nextInstructorOpening &&
                  nextInstructorOpening?.all_slots &&
                  nextInstructorOpening?.all_slots?.length > 0 ? (
                  <div className="space-y-3">
                    <div className="font-helvetica flex items-center justify-between text-sm text-gray-600">
                      <span>Available times</span>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-[#ddba0a]"></div>
                          <span className="text-xs">Peak</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className="h-3 w-3 rounded-full bg-gray-300"></div>
                          <span className="text-xs">Unavailable</span>
                        </div>
                      </div>
                    </div>
                    <div className="scrollbar-hide grid max-h-[300px] grid-cols-3 gap-2 overflow-y-auto lg:grid-cols-4 xl:grid-cols-6">
                      {nextInstructorOpening.all_slots.map((slot: any, index: number) => {
                        const isAvailable = nextInstructorOpening?.possible_slots?.some(
                          (availableSlot: any) => availableSlot.start === slot.start
                        );

                        return (
                          <button
                            key={index}
                            onClick={() =>
                              isAvailable && updateLessonBookingDetails({ time: slot?.start })
                            }
                            disabled={!isAvailable}
                            className={`font-helvetica relative rounded-[12px] px-3 py-2 text-sm font-normal transition-all duration-200 ${
                              isAvailable
                                ? `cursor-pointer border border-[#c3c3c3] ${lessonBookingDetails?.time === slot?.start ? "border-[#1c5534] bg-[#1c5534] text-white" : "bg-white text-black hover:border-[#1c5534] hover:bg-gray-50"} `
                                : "cursor-not-allowed border border-gray-200 bg-gray-100 text-gray-400"
                            }`}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-[13px] leading-tight">{slot?.start}</span>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center py-8">
                    <span className="font-helvetica text-[15px] font-normal text-[#c3c3c3]">
                      No time slots available
                    </span>
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Divider - Hidden on mobile, visible on desktop */}
          <div
            className="hidden h-full w-[2px] rounded-full bg-[#EBEBEB] lg:block"
            data-svg-wrapper
          ></div>

          {/* Right Section - Lesson Image */}
          <div className="flex w-full flex-col items-center justify-center lg:w-1/2">
            {/* Duration Selection */}
            <div className="font-helvetica w-full pb-2 text-base leading-[35px] font-normal text-black">
              Select duration
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="flex w-full items-center justify-between rounded-[20px] border border-[#c3c3c3] bg-white px-6 py-2 transition-colors hover:bg-gray-50">
                <span className="font-helvetica text-[15px] leading-[35px] font-normal text-black">
                  {lessonBookingDetails?.duration
                    ? `${lessonBookingDetails.duration} minutes`
                    : "Choose duration"}
                </span>
                <ChevronDown className="h-4 w-4 text-gray-500" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[var(--radix-dropdown-menu-trigger-width)] rounded-[20px] border border-[#c3c3c3] bg-white p-2 shadow-lg">
                {durationOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => handleDurationSelect(option.value)}
                    className="font-helvetica cursor-pointer rounded-[15px] px-4 py-2 text-[15px] font-normal text-black transition-colors hover:bg-gray-50 focus:bg-gray-50"
                  >
                    {option.label}
                    {option.isDefault && (
                      <span className="ml-2 text-xs text-gray-500">(Default)</span>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {lesson.max_player > 1 && (
              <div className="flex w-full flex-col gap-4 pt-6">
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-base leading-[35px] font-normal text-black">
                    Number of Players
                  </div>
                  <div className="flex items-center justify-center gap-4">
                    <button
                      onClick={() =>
                        updateLessonBookingDetails({
                          players: Math.max(
                            lesson.min_player,
                            (lessonBookingDetails?.players || lesson.min_player) - 1
                          ),
                        })
                      }
                      disabled={
                        (lessonBookingDetails?.players || lesson.min_player) <= lesson.min_player
                      }
                      className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                    >
                      <Minus className="h-4 w-4 text-black" />
                    </button>
                    <div className="font-helvetica min-w-[60px] text-center text-[15px] leading-[35px] font-normal text-black">
                      {lessonBookingDetails?.players || lesson.min_player}
                    </div>
                    <button
                      onClick={() =>
                        updateLessonBookingDetails({
                          players: Math.min(
                            lesson.max_player,
                            (lessonBookingDetails?.players || lesson.min_player) + 1
                          ),
                        })
                      }
                      disabled={
                        (lessonBookingDetails?.players || lesson.min_player) >= lesson.max_player
                      }
                      className="flex h-[42px] w-[42px] items-center justify-center rounded-full border border-[#D8D8D8] bg-white transition-colors hover:bg-gray-50"
                    >
                      <Plus className="h-4 w-4 text-black" />
                    </button>
                  </div>
                </div>

                {/* Add Player Button */}
                <div className="flex w-full items-center justify-between">
                  <div className="font-helvetica text-sm text-gray-600">
                    {selectedParticipants.length > 0
                      ? `${selectedParticipants.length} participant${selectedParticipants.length > 1 ? "s" : ""} selected`
                      : "No participants selected"}
                  </div>
                  <button
                    onClick={handleOpenPlayerDialog}
                    className="hover:bg-primary flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium text-[#1c5534] transition-colors hover:text-white"
                  >
                    <Plus className="h-4 w-4" />
                    Add Players
                  </button>
                </div>
              </div>
            )}

            {/* Booking Details Section */}
            <div className="mt-4 flex w-full flex-col items-start justify-start gap-10 py-2">
              <div className="flex w-full flex-col items-start justify-between gap-4 rounded-[20px] border-[#ddba0a] bg-[#fffaed] p-4 shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)] sm:flex-row sm:items-end sm:p-6">
                <div className="flex-1">
                  <p className="font-helvetica text-base leading-snug font-medium text-black">
                    Lesson Booking Details
                  </p>
                  {!lessonBookingDetails?.instructor_name &&
                    !lessonBookingDetails?.date &&
                    !lessonBookingDetails?.time &&
                    !lessonBookingDetails?.duration && (
                      <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                        Please select instructor, date, time and duration
                      </p>
                    )}
                  {lessonBookingDetails?.instructor_name && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Instructor: {lessonBookingDetails.instructor_name}
                    </p>
                  )}
                  {lessonBookingDetails?.date && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Date: {lessonBookingDetails.date}
                    </p>
                  )}
                  {lessonBookingDetails?.time && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Time: {lessonBookingDetails.time}
                    </p>
                  )}
                  {lessonBookingDetails?.duration && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Duration: {lessonBookingDetails.duration} minutes
                    </p>
                  )}
                  {lessonBookingDetails?.players && lessonBookingDetails.players > 1 && (
                    <p className="font-helvetica text-[15px] leading-[21px] font-normal text-black">
                      Players: {lessonBookingDetails.players}
                    </p>
                  )}
                </div>
                <div className="flex w-full max-w-[200px] flex-col items-center justify-center gap-1 py-2.5 sm:w-auto">
                  {estimateLoading ? (
                    <Skeleton className="h-10 w-20 rounded-full" />
                  ) : estimateError ? (
                    <div className="font-helvetica text-center text-sm leading-normal text-red-500 capitalize">
                      {estimateError}
                    </div>
                  ) : (
                    estimateData && (
                      <div className="font-helvetica max-w-[200px] text-center text-[22px] leading-normal font-bold text-[#1c5534] lg:max-w-sm">
                        Pay ${estimateData?.total.toFixed(2)}
                      </div>
                    )
                  )}
                </div>
                {/* <div className="flex w-full flex-col items-center justify-center gap-1 py-2.5 sm:w-auto">
                  <div className="font-helvetica text-center text-[22px] leading-normal font-bold text-[#1c5534]">
                    Pay ${pricing.lessonBooking.toFixed(2)}
                  </div>
                </div> */}
              </div>
              <button
                onClick={handleBookNow}
                disabled={
                  isBooking ||
                  !lessonBookingDetails?.instructor_name ||
                  !lessonBookingDetails?.date ||
                  !lessonBookingDetails?.time ||
                  !lessonBookingDetails?.duration ||
                  !(
                    lessonBookingDetails?.players <= 1 ||
                    selectedParticipants.length ===
                      (lessonBookingDetails?.players || lesson.min_player)
                  ) ||
                  !estimateData ||
                  estimateLoading ||
                  estimateError != null
                }
                className="font-helvetica flex w-full items-center justify-center gap-2.5 rounded-[30px] bg-[#ddba0a] px-4 py-3 text-[15px] font-bold tracking-tight text-[#1c5534] transition-colors hover:bg-[#c4a609] disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isBooking ? <Loader /> : "Book Now"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Player Management Dialog */}
      <LessonPlayerManagementDialog
        isOpen={isPlayerDialogOpen}
        onClose={handleClosePlayerDialog}
        players={players}
        onPlayerToggle={handlePlayerToggle}
        selectedPlayersCount={selectedParticipants.length}
        maxPlayers={lessonBookingDetails?.players || lesson.min_player}
        playerListLoading={playerListLoading}
        revalidateGetPlayerList={revalidateGetPlayerList}
        onPlayerAdded={handlePlayerAdded}
      />
    </div>
  );
};

export default LessonDetailsStep;
