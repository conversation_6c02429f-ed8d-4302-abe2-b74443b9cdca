import { CourtBookingDetails } from "@/store/booking-store";
import { useMemo } from "react";
import useSWR, { mutate } from "swr";
import axiosInstance, { endpoints, epicFetcher } from "./axios";

export function useGetPublicCourtLocations({ page, limit }: { page?: number; limit?: number } = { page: 1, limit: 10 }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicCourtLocations}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtLocations = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicCourtLocationList: queueData,
            publicCourtLocationListLoading: isLoading,
            publicCourtLocationListError: error,
            publicCourtLocationListValidating: isValidating,
            publicCourtLocationListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtLocations,
    };
}


export function useGetPublicCourtList({ page, limit, courtLocationId }: { page: number; limit: number, courtLocationId?: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getAllPublicCourtList}?page=${page}&limit=${limit}&court_location_id=${courtLocationId}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicCourtList: queueData,
            publicCourtListLoading: isLoading,
            publicCourtListError: error,
            publicCourtListValidating: isValidating,
            publicCourtListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtList,
    };
}


export function useGetPublicCourtById({ courtId }: { courtId: number | null }) {

    const fullUrl = useMemo(
        () => courtId ? `${endpoints.booking.getPublicCourtById}/${courtId}` : null,
        [courtId]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicCourtDetails = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            publicCourtDetails: queueData,
            publicCourtDetailsLoading: isLoading,
            publicCourtDetailsError: error,
            publicCourtDetailsValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicCourtDetails,
    };
}

export function useGetPublicNextOpeningNew({ date, courtId, bookingPriceId, subCourtId }: { date: string; courtId: number | null; bookingPriceId: number; subCourtId: number }) {

    const fullUrl = useMemo(
        () => {
            if (!date || !courtId || !bookingPriceId || !subCourtId) {
                return null;
            }
            return `${endpoints.booking.getPublicNextOpeningNew}?date=${date}&court_id=${courtId}&booking_price_id=${bookingPriceId}&sub_court_id=${subCourtId}`
        },
        [date, courtId, bookingPriceId, subCourtId]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicNextOpeningNew = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            publicNextOpeningNew: queueData,
            publicNextOpeningNewLoading: isLoading,
            publicNextOpeningNewError: error,
            publicNextOpeningNewValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicNextOpeningNew,
    };
}

export function useGetNextInstructorOpening({ date, instructorId, subCourtId, lessonTypeId, duration }: { date: string; lessonTypeId: number | null; instructorId: number | null; subCourtId: number | null; duration: number | null }) {

    const fullUrl = useMemo(
        () => {
            if (!date || !lessonTypeId || !instructorId || !subCourtId || !duration) {
                return null;
            }
            return `${endpoints.booking.getNextInstructorOpening}?instructor_id=${instructorId}&date=${date}&sub_court_id=${subCourtId}&lesson_type_id=${lessonTypeId}&duration=${duration}`
        },
        [date, lessonTypeId, instructorId, subCourtId, duration]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetNextInstructorOpening = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            nextInstructorOpening: queueData,
            nextInstructorOpeningLoading: isLoading,
            nextInstructorOpeningError: error,
            nextInstructorOpeningValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetNextInstructorOpening,
    };
}

export function useGetBookingDetailsById({ bookingId }: { bookingId: number }) {

    const fullUrl = useMemo(
        () => bookingId ? `${endpoints.booking.getBookingDetailsById}/${bookingId}` : null,
        [bookingId]
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetBookingDetails = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || {};
        return {
            bookingDetails: queueData,
            bookingDetailsLoading: isLoading,
            bookingDetailsError: error,
            bookingDetailsValidating: isValidating,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetBookingDetails,
    };
}


export async function createCourtBooking(bookingData: CourtBookingDetails): Promise<any> {
    const payload: any = {
        court_id: bookingData.court_id,
        sub_court_id: bookingData.sub_court_id,
        start_time: bookingData.start_time,
        end_time: bookingData.end_time,
        split: bookingData.split,

    }

    if (bookingData.attributes && bookingData.attributes.length > 0) {
        payload["attributes"] = bookingData.attributes;
    }
    if (bookingData.split && bookingData.guest_users && bookingData.guest_users.length > 0) {
        payload["guest_users"] = bookingData.guest_users?.map((id) => ({ id }));
    }
    const response = await axiosInstance.post(endpoints.booking.createCourtBooking, payload);
    return response;
}

export async function createLessonBooking(bookingData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.createLessonBooking, bookingData);
    return response;
}


export async function extimateCourtBooking(exitimateData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.extimateCourtBooking, exitimateData);
    return response;
}

export async function estimateLessonBooking(estimateData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.extimateLessonBooking, estimateData);
    return response;
}

export async function estimateProgramBooking(estimateData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.extimateProgramBooking, estimateData);
    return response;
}


export async function confirmLessonCheckout(checkoutData: any): Promise<any> {
    const response = await axiosInstance.post(endpoints.booking.confirmLessonCheckout, checkoutData);
    return response;
}

