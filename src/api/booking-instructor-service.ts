import { useMemo } from "react";
import useS<PERSON>, { mutate } from "swr";
import { endpoints, epicFetcher } from "./axios";

export function useGetPublicInstructorList({ page, limit }: { page: number; limit: number }) {

    const fullUrl = useMemo(

        () => `${endpoints.booking.getPublicInstructorList}?page=${page}&limit=${limit}`,
        []
    );

    const { data, error, isLoading, isValidating } = useSWR(
        fullUrl,
        epicFetcher,
        {
            revalidateOnFocus: false,
        }
    );

    const revalidateGetPublicInstructorList = async () => {
        await mutate(fullUrl);
    };

    const memoizedValue = useMemo(() => {
        const queueData = data?.data || [];
        return {
            publicInstructorList: queueData,
            publicInstructorListLoading: isLoading,
            publicInstructorListError: error,
            publicInstructorListValidating: isValidating,
            publicInstructorListEmpty: queueData.length === 0,
        };
    }, [data?.data, error, isLoading, isValidating]);

    return {
        ...memoizedValue,
        revalidateGetPublicInstructorList,
    };
}