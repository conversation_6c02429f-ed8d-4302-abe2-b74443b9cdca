import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { extimateCourtBooking, estimateLessonBooking, estimateProgramBooking } from "@/api/booking-service";
import { extractErrorMessage } from "@/libs/utils";
import { BookingEstimateData, BookingEstimateResponse } from "@/components/checkout/summary-section";
import { LessonBookingEstimateData, LessonBookingEstimateResponse } from "@/components/checkout/lesson-summary-section";
import { ProgramType } from "@/types/booking";
import { ProgramBookingEstimateData, ProgramBookingEstimateResponse } from "@/components/checkout/program-summary-section";

export interface BookingEstimateRequest {
  court_id: number;
  sub_court_id: number;
  start_time: string;
  end_time: string;
  split: boolean;
  attributes?: Array<{
    court_attribute_id: number;
    value: number;
  }>;
  guest_users?: Array<{ id: number }>;
}

export interface LessonBookingEstimateRequest {
  instructor_id: number;
  lesson_type_id: number;
  type: string;
  start_time: string;
  duration: number;
  sub_court_id: number;
}

export interface ProgramBookingEstimateRequest {
  payment_type: ProgramType;
  program_id: number;
  program_class_id: number[];
  use_wallet: boolean;
}

export interface UseBookingEstimateReturn {
  estimateData: BookingEstimateData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseLessonBookingEstimateReturn {
  estimateData: LessonBookingEstimateData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export interface UseProgramBookingEstimateReturn {
  estimateData: ProgramBookingEstimateData | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useBookingEstimate = (
  requestData: BookingEstimateRequest | null
): UseBookingEstimateReturn => {
  const [estimateData, setEstimateData] = useState<BookingEstimateData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEstimate = useCallback(async () => {
    if (!requestData || !requestData.court_id || !requestData.sub_court_id || !requestData.start_time
    ) {
      setEstimateData(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response: BookingEstimateResponse = await extimateCourtBooking(requestData);

      if (response.data) {
        setEstimateData(response.data?.data);
      } else {
        setError('Failed to get booking estimate');
        setEstimateData(null);
      }
    } catch (err) {
      const errorMessage = extractErrorMessage(err);
      setError(errorMessage);
      setEstimateData(null);
      console.error('Error fetching booking estimate:', err);
    } finally {
      setIsLoading(false);
    }
  }, [requestData]);

  // Auto-fetch when request data changes
  useEffect(() => {
    fetchEstimate();
  }, [fetchEstimate]);

  return {
    estimateData,
    isLoading,
    error,
    refetch: fetchEstimate,
  };
};

export const useLessonBookingEstimate = (requestData: LessonBookingEstimateRequest | null): UseLessonBookingEstimateReturn => {
  const [estimateData, setEstimateData] = useState<LessonBookingEstimateData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEstimate = useCallback(async () => {
    if (!requestData || !requestData.instructor_id || !requestData.lesson_type_id || !requestData.start_time
      || !requestData.duration || !requestData.sub_court_id
    ) {
      setEstimateData(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response: LessonBookingEstimateResponse = await estimateLessonBooking(requestData);

      if (response.data) {
        setEstimateData(response.data?.data);
      } else {
        setError('Failed to get lesson booking estimate');
        setEstimateData(null);
      }
    } catch (err) {
      const errorMessage = extractErrorMessage(err);
      setError(errorMessage);
      setEstimateData(null);
      console.error('Error fetching lesson booking estimate:', err);
    } finally {
      setIsLoading(false);
    }
  }, [requestData, requestData?.instructor_id, requestData?.lesson_type_id, requestData?.start_time, requestData?.duration, requestData?.sub_court_id]);

  // Auto-fetch when request data changes
  useEffect(() => {
    fetchEstimate();
  }, [fetchEstimate]);

  return {
    estimateData,
    isLoading,
    error,
    refetch: fetchEstimate,
  };
};


export const useProgramBookingEstimate = (requestData: ProgramBookingEstimateRequest | null): UseProgramBookingEstimateReturn => {
  const [estimateData, setEstimateData] = useState<ProgramBookingEstimateData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchEstimate = useCallback(async () => {
    if (!requestData) {
      setEstimateData(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response: ProgramBookingEstimateResponse = await estimateProgramBooking(requestData);

      if (response.data) {
        setEstimateData(response.data?.data);
      } else {
        setError('Failed to get program booking estimate');
        setEstimateData(null);
      }
    } catch (err) {
      const errorMessage = extractErrorMessage(err);
      setError(errorMessage);
      setEstimateData(null);
      console.error('Error fetching program booking estimate:', err);
    } finally {
      setIsLoading(false);
    }
  }, [requestData]);

  useEffect(() => {
    fetchEstimate();
  }, [fetchEstimate]);

  return {
    estimateData,
    isLoading,
    error,
    refetch: fetchEstimate,
  };
};